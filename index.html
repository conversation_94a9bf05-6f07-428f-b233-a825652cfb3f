<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estúdio730 - Links</title>
    <meta name="description" content="Todos os links do Estúdio730 em um só lugar">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>✂️</text></svg>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Botão de Configurações -->
    <button class="config-button" id="config-button" title="Configurações">
        <i class="fas fa-cog"></i>
    </button>

    <!-- Container principal -->
    <div class="container">
        <!-- Header com logo e nome da barbearia -->
        <header class="header">
            <div class="logo">
                <img src="logo.webp" alt="Estúdio730 Logo" class="logo-image">
            </div>
            <h1 class="title" id="main-title">Estúdio730</h1>
            <p class="subtitle">Estilo e tradição em cada corte</p>
        </header>

        <!-- Seção de links principais -->
        <main class="links-section">
            <!-- Botão WhatsApp -->
            <a href="#" class="link-button whatsapp" id="whatsapp-btn" target="_blank" rel="noopener noreferrer">
                <div class="button-content">
                    <i class="fab fa-whatsapp"></i>
                    <span class="button-text">
                        <strong>WhatsApp</strong>
                        <small>Agende seu horário</small>
                    </span>
                </div>
                <i class="fas fa-chevron-right arrow"></i>
            </a>

            <!-- Botão Instagram -->
            <a href="#" class="link-button instagram" id="instagram-btn" target="_blank" rel="noopener noreferrer">
                <div class="button-content">
                    <i class="fab fa-instagram"></i>
                    <span class="button-text">
                        <strong>Instagram</strong>
                        <small>Veja nossos trabalhos</small>
                    </span>
                </div>
                <i class="fas fa-chevron-right arrow"></i>
            </a>

            <!-- Botão Localização -->
            <a href="#" class="link-button location" id="location-btn" target="_blank" rel="noopener noreferrer">
                <div class="button-content">
                    <i class="fas fa-map-marker-alt"></i>
                    <span class="button-text">
                        <strong>Localização</strong>
                        <small>Como chegar</small>
                    </span>
                </div>
                <i class="fas fa-chevron-right arrow"></i>
            </a>

            <!-- Botão Site -->
            <a href="#" class="link-button website" id="website-btn" target="_blank" rel="noopener noreferrer">
                <div class="button-content">
                    <i class="fas fa-globe"></i>
                    <span class="button-text">
                        <strong>Site Oficial</strong>
                        <small>Conheça nossos serviços</small>
                    </span>
                </div>
                <i class="fas fa-chevron-right arrow"></i>
            </a>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 Estúdio730. Todos os direitos reservados.</p>
            <div class="social-icons">
                <i class="fab fa-instagram"></i>
                <i class="fab fa-facebook"></i>
                <i class="fab fa-whatsapp"></i>
            </div>
        </footer>
    </div>

    <!-- Modal de Configurações -->
    <div class="config-modal" id="config-modal">
        <div class="config-modal-content">
            <!-- Indicador de Swipe para Mobile -->
            <div class="swipe-indicator mobile-only">
                <div class="swipe-handle"></div>
            </div>

            <!-- Header do Modal -->
            <div class="config-header">
                <h2><i class="fas fa-cog"></i> Configurações</h2>
                <button class="config-close" id="config-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Conteúdo do Modal com Navegação por Abas -->
            <div class="config-body">
                <!-- Navegação por Abas -->
                <div class="config-tabs">
                    <div class="tab-navigation" id="tab-navigation" role="tablist">
                        <button class="tab-btn active" data-tab="manage" role="tab" aria-controls="manage-panel">
                            <i class="fas fa-link"></i>
                            <span>Gerenciar</span>
                        </button>
                        <button class="tab-btn" data-tab="add" role="tab" aria-controls="add-panel">
                            <i class="fas fa-plus"></i>
                            <span>Adicionar</span>
                        </button>
                        <button class="tab-btn" data-tab="settings" role="tab" aria-controls="settings-panel">
                            <i class="fas fa-cog"></i>
                            <span>Config</span>
                        </button>
                    </div>

                    <!-- Conteúdo das Abas -->
                    <div class="tab-content">
                        <!-- Aba Gerenciar Links -->
                        <div class="tab-panel active" id="manage-panel" role="tabpanel">
                            <div class="config-section">
                                <h3 class="section-title desktop-only"><i class="fas fa-link"></i> Gerenciar Links</h3>
                                <div class="links-list" id="links-list">
                                    <!-- Links serão inseridos dinamicamente aqui -->
                                </div>
                            </div>
                        </div>

                        <!-- Aba Adicionar Link -->
                        <div class="tab-panel" id="add-panel" role="tabpanel">
                            <div class="config-section">
                                <h3 class="section-title desktop-only"><i class="fas fa-plus"></i> Adicionar Novo Link</h3>

                                <!-- Botão para expandir formulário -->
                                <button class="btn-expand-form" id="btn-expand-form">
                                    <i class="fas fa-plus"></i> Adicionar Novo Link
                                </button>

                                <!-- Formulário expansível -->
                                <div class="add-link-form-container" id="add-link-form-container">
                        <form class="add-link-form" id="add-link-form">
                            <div class="form-group">
                                <label for="link-name">Nome do Serviço *</label>
                                <input type="text" id="link-name" placeholder="Ex: Facebook, TikTok, Agendamento" required>
                            </div>

                            <div class="form-group">
                                <label for="link-url">URL do Link *</label>
                                <input type="url" id="link-url" placeholder="https://..." required>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="link-icon">Ícone do Serviço</label>
                                    <div class="icon-selector">
                                        <select id="link-icon" required>
                                            <option value="">Selecione um ícone</option>
                                            <option value="fab fa-facebook" data-color="#1877f2">Facebook</option>
                                            <option value="fab fa-instagram" data-color="#e4405f">Instagram</option>
                                            <option value="fab fa-twitter" data-color="#1da1f2">Twitter/X</option>
                                            <option value="fab fa-tiktok" data-color="#000000">TikTok</option>
                                            <option value="fab fa-youtube" data-color="#ff0000">YouTube</option>
                                            <option value="fab fa-linkedin" data-color="#0077b5">LinkedIn</option>
                                            <option value="fab fa-whatsapp" data-color="#25d366">WhatsApp</option>
                                            <option value="fab fa-telegram" data-color="#0088cc">Telegram</option>
                                            <option value="fas fa-envelope" data-color="#ea4335">Email</option>
                                            <option value="fas fa-phone" data-color="#34a853">Telefone</option>
                                            <option value="fas fa-globe" data-color="#6c5ce7">Site/Link</option>
                                            <option value="fas fa-map-marker-alt" data-color="#4285f4">Localização</option>
                                            <option value="fas fa-calendar" data-color="#fbbc04">Calendário</option>
                                            <option value="fas fa-shopping-cart" data-color="#ff6900">Loja</option>
                                            <option value="fas fa-link" data-color="#6c5ce7">Link Genérico</option>
                                        </select>
                                        <div class="icon-preview" id="icon-preview">
                                            <i class="fas fa-question"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="link-color">Cor do Botão</label>
                                    <input type="color" id="link-color" value="#6c5ce7">
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn-cancel-form" id="btn-cancel-form">
                                    <i class="fas fa-times"></i> Cancelar
                                </button>
                                <button type="submit" class="btn-add-link">
                                    <i class="fas fa-plus"></i> Adicionar Link
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Aba Configurações -->
            <div class="tab-panel" id="settings-panel" role="tabpanel">
                <div class="config-section">
                    <h3 class="section-title desktop-only"><i class="fas fa-cog"></i> Configurações Avançadas</h3>

                    <div class="settings-options">
                        <div class="setting-item">
                            <div class="setting-icon">
                                <i class="fas fa-palette"></i>
                            </div>
                            <div class="setting-content">
                                <div class="setting-info">
                                    <h4>Tema da Interface</h4>
                                    <small>Escolha entre tema claro ou escuro</small>
                                </div>
                                <div class="setting-actions">
                                    <select id="theme-selector" class="form-select">
                                        <option value="dark">Escuro</option>
                                        <option value="light">Claro</option>
                                        <option value="auto">Automático</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-icon">
                                <i class="fas fa-magic"></i>
                            </div>
                            <div class="setting-content">
                                <div class="setting-info">
                                    <h4>Animações</h4>
                                    <small>Ativar/desativar efeitos visuais</small>
                                </div>
                                <div class="setting-actions">
                                    <div class="toggle-switch active" id="animations-toggle">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="setting-content">
                                <div class="setting-info">
                                    <h4>Backup Automático</h4>
                                    <small>Salvar configurações automaticamente</small>
                                </div>
                                <div class="setting-actions">
                                    <div class="toggle-switch active" id="backup-toggle">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer do Modal -->
            <div class="config-footer">
                <button class="btn-secondary" id="btn-restore">
                    <i class="fas fa-undo"></i> Restaurar Padrões
                </button>
                <div class="footer-actions">
                    <button class="btn-secondary" id="btn-cancel">Cancelar</button>
                    <button class="btn-primary" id="btn-save">
                        <i class="fas fa-save"></i> Salvar Configurações
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Edição de Link -->
    <div class="edit-modal" id="edit-modal">
        <div class="edit-modal-content">
            <div class="edit-header">
                <h3><i class="fas fa-edit"></i> Editar Link</h3>
                <button class="edit-close" id="edit-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="edit-body">
                <form class="edit-link-form" id="edit-link-form">
                    <input type="hidden" id="edit-link-id">

                    <div class="form-group">
                        <label for="edit-link-name">Nome do Serviço *</label>
                        <input type="text" id="edit-link-name" required>
                    </div>

                    <div class="form-group">
                        <label for="edit-link-url">URL do Link *</label>
                        <input type="url" id="edit-link-url" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit-link-icon">Ícone do Serviço</label>
                            <div class="icon-selector">
                                <select id="edit-link-icon" required>
                                    <option value="">Selecione um ícone</option>
                                    <option value="fab fa-facebook" data-color="#1877f2">Facebook</option>
                                    <option value="fab fa-instagram" data-color="#e4405f">Instagram</option>
                                    <option value="fab fa-twitter" data-color="#1da1f2">Twitter/X</option>
                                    <option value="fab fa-tiktok" data-color="#000000">TikTok</option>
                                    <option value="fab fa-youtube" data-color="#ff0000">YouTube</option>
                                    <option value="fab fa-linkedin" data-color="#0077b5">LinkedIn</option>
                                    <option value="fab fa-whatsapp" data-color="#25d366">WhatsApp</option>
                                    <option value="fab fa-telegram" data-color="#0088cc">Telegram</option>
                                    <option value="fas fa-envelope" data-color="#ea4335">Email</option>
                                    <option value="fas fa-phone" data-color="#34a853">Telefone</option>
                                    <option value="fas fa-globe" data-color="#6c5ce7">Site/Link</option>
                                    <option value="fas fa-map-marker-alt" data-color="#4285f4">Localização</option>
                                    <option value="fas fa-calendar" data-color="#fbbc04">Calendário</option>
                                    <option value="fas fa-shopping-cart" data-color="#ff6900">Loja</option>
                                    <option value="fas fa-link" data-color="#6c5ce7">Link Genérico</option>
                                </select>
                                <div class="icon-preview" id="edit-icon-preview">
                                    <i class="fas fa-question"></i>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="edit-link-color">Cor do Botão</label>
                            <input type="color" id="edit-link-color">
                        </div>
                    </div>

                    <div class="edit-actions">
                        <button type="button" class="btn-secondary" id="btn-cancel-edit">
                            Cancelar
                        </button>
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-save"></i> Salvar Alterações
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script src="config-system.js"></script>
</body>
</html>
